#!/bin/bash

# =============================================================================
# ZURICH WORKFLOW SETUP AND DEPLOYMENT SCRIPT
# =============================================================================
# This script helps set up the environment and deploy the zurich-workflow
# following the same pattern as rozie-air

set -e  # Exit on any error

echo "🚀 Setting up Zurich Workflow Deployment..."

# =============================================================================
# STEP 1: GET AWS ACCOUNT INFO
# =============================================================================
echo "📋 Getting AWS account information..."

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION="ca-central-1"

echo "✅ AWS Account ID: $AWS_ACCOUNT_ID"
echo "✅ AWS Region: $AWS_REGION"

# =============================================================================
# STEP 2: FIND ACM CERTIFICATE
# =============================================================================
echo "🔍 Looking for ACM certificate for *.dev-scc-demo.rozie.ai..."

# Try to find the certificate in ca-central-1
ACM_CERT_ARN=$(aws acm list-certificates --region ca-central-1 \
  --query "CertificateSummaryList[?DomainName=='*.dev-scc-demo.rozie.ai'].CertificateArn" \
  --output text 2>/dev/null || echo "")

if [ -z "$ACM_CERT_ARN" ]; then
  echo "⚠️  Certificate not found in ca-central-1, checking us-east-1..."
  ACM_CERT_ARN=$(aws acm list-certificates --region us-east-1 \
    --query "CertificateSummaryList[?DomainName=='*.dev-scc-demo.rozie.ai'].CertificateArn" \
    --output text 2>/dev/null || echo "")
fi

if [ -z "$ACM_CERT_ARN" ]; then
  echo "❌ ACM Certificate not found. You may need to:"
  echo "   1. Create a wildcard certificate for *.dev-scc-demo.rozie.ai"
  echo "   2. Or use an existing certificate ARN"
  echo "   3. Or deploy without custom domains first"
  ACM_CERT_ARN="arn:aws:acm:ca-central-1:$AWS_ACCOUNT_ID:certificate/your-certificate-id"
else
  echo "✅ Found ACM Certificate: $ACM_CERT_ARN"
fi

# =============================================================================
# STEP 3: FIND HOSTED ZONE
# =============================================================================
echo "🔍 Looking for Route53 hosted zone for dev-scc-demo.rozie.ai..."

HOSTED_ZONE_ID=$(aws route53 list-hosted-zones \
  --query "HostedZones[?Name=='dev-scc-demo.rozie.ai.'].Id" \
  --output text 2>/dev/null | sed 's|/hostedzone/||' || echo "")

if [ -z "$HOSTED_ZONE_ID" ]; then
  echo "❌ Hosted Zone not found. Using placeholder."
  HOSTED_ZONE_ID="Z0532137UPF6P3UT464"
else
  echo "✅ Found Hosted Zone: $HOSTED_ZONE_ID"
fi

# =============================================================================
# STEP 4: UPDATE .ENV FILE
# =============================================================================
echo "📝 Updating .env file with actual values..."

# Update AWS Account ID
sed -i.bak "s/AWS_ACCOUNT_ID=.*/AWS_ACCOUNT_ID=$AWS_ACCOUNT_ID/" .env

# Update ACM Certificate ARN
sed -i.bak "s|acm-cert-arn=.*|acm-cert-arn=$ACM_CERT_ARN|" .env

# Update Hosted Zone ID
sed -i.bak "s/hosted-zone-id=.*/hosted-zone-id=$HOSTED_ZONE_ID/" .env

# Update ECR URIs with correct account ID and region
sed -i.bak "s|BACKEND_ECR_REPO_URI=.*|BACKEND_ECR_REPO_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/rozieai-zurich-uc05-backend-dev|" .env
sed -i.bak "s|DASHBOARD_ECR_REPO_URI=.*|DASHBOARD_ECR_REPO_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/rozieai-zurich-uc05-dashboard-dev|" .env
sed -i.bak "s|N8N_ECR_REPO_URI=.*|N8N_ECR_REPO_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/rozieai-zurich-uc05-n8n-dev|" .env

echo "✅ Updated .env file"

# =============================================================================
# STEP 5: SHOW CURRENT CONFIGURATION
# =============================================================================
echo "📋 Current configuration:"
echo "   AWS Account ID: $AWS_ACCOUNT_ID"
echo "   AWS Region: $AWS_REGION"
echo "   Hosted Zone ID: $HOSTED_ZONE_ID"
echo "   ACM Certificate: $ACM_CERT_ARN"
echo ""

# =============================================================================
# STEP 6: DEPLOYMENT OPTIONS
# =============================================================================
echo "🚀 Ready to deploy! Choose an option:"
echo ""
echo "1. Deploy with custom domains (requires valid ACM certificate)"
echo "2. Deploy without custom domains (simpler, no DNS setup needed)"
echo "3. Just show environment variables (no deployment)"
echo ""

read -p "Enter your choice (1-3): " choice

case $choice in
  1)
    echo "🚀 Deploying with custom domains..."
    echo "⚠️  Make sure your ACM certificate is valid!"
    
    # Clean up any previous failed deployments
    echo "🧹 Cleaning up previous deployments..."
    serverless remove --stage dev || echo "No previous deployment to remove"
    
    # Deploy
    echo "🚀 Deploying..."
    serverless deploy --stage dev
    ;;
  2)
    echo "🚀 Deploying without custom domains..."
    echo "📝 This will comment out domain-related resources temporarily"
    
    # TODO: Add logic to comment out domain resources in serverless.yml
    echo "❌ This option is not implemented yet."
    echo "   Please manually comment out the domain-related resources in serverless.yml"
    echo "   or use option 1 with a valid certificate."
    ;;
  3)
    echo "📋 Environment variables:"
    echo "export AWS_ACCOUNT_ID=$AWS_ACCOUNT_ID"
    echo "export AWS_REGION=$AWS_REGION"
    echo "export hosted-zone-id=$HOSTED_ZONE_ID"
    echo "export acm-cert-arn=$ACM_CERT_ARN"
    ;;
  *)
    echo "❌ Invalid choice. Exiting."
    exit 1
    ;;
esac

echo "✅ Done!"
