# =============================================================================
# ZURICH WORKFLOW DEPLOYMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
AWS_REGION=ca-central-1
AWS_ACCOUNT_ID=your-aws-account-id

# =============================================================================
# ECR REPOSITORIES
# =============================================================================
# Backend API ECR Repository
BACKEND_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/rozieai-zurich-uc05-backend

# Dashboard ECR Repository
DASHBOARD_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/rozieai-zurich-uc05-dashboard

# N8N ECR Repository
N8N_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/rozieai-zurich-uc05-n8n

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
# Backend API Domain
backend-domain-name=rozieai-zurich-uc05-api.yourdomain.com

# Dashboard Domain
dashboard-domain-name=rozieai-zurich-uc05-dashboard.yourdomain.com

# N8N Domain
n8n-domain-name=rozieai-zurich-uc05-n8n.yourdomain.com

# Frontend Domain
frontend-domain-name=rozieai-zurich-uc05-app.yourdomain.com

# Route53 Hosted Zone ID
hosted-zone-id=your-hosted-zone-id

# ACM Certificate ARN (must cover all subdomains)
acm-cert-arn=arn:aws:acm:ca-central-1:your-account-id:certificate/your-cert-id

# =============================================================================
# S3 CONFIGURATION
# =============================================================================
# Frontend S3 Bucket Name
frontend-s3-bucket=rozieai-zurich-uc05-frontend-dev

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key

# BAML Configuration
BAML_ENVIRONMENT=development

# FastAPI Configuration
FASTAPI_ENV=development
LOG_LEVEL=INFO

# Node.js Configuration
NODE_ENV=production

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# =============================================================================
# SUPPORT SYSTEM CONFIGURATION
# =============================================================================
SUPPORT_SUBDOMAIN=d3v-rozieai5417
SUPPORT_EMAIL=<EMAIL>
SUPPORT_TOKEN=your-support-token

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Deployment stage (dev, staging, prod)
STAGE=dev

# GitHub Configuration (for CI/CD)
GITHUB_TOKEN=your-github-token
GITHUB_REPOSITORY=your-org/your-repo

# =============================================================================
# N8N WORKFLOW AUTOMATION CONFIGURATION
# =============================================================================
# N8N Encryption Key (REQUIRED - generate a random 32-character string)
N8N_ENCRYPTION_KEY=your-32-character-encryption-key-here

# N8N Timezone
GENERIC_TIMEZONE=America/Toronto

# N8N Logging and Monitoring
N8N_LOG_LEVEL=info
N8N_METRICS=false
N8N_DIAGNOSTICS_ENABLED=false
N8N_VERSION_NOTIFICATIONS_ENABLED=false

# N8N Features
N8N_TEMPLATES_ENABLED=true
N8N_PUBLIC_API_DISABLED=false
N8N_DISABLE_UI=false

# N8N Domain (already configured above)
# n8n-domain-name=rozieai-zurich-uc05-n8n.yourdomain.com

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this file with real values to version control
# 2. Use AWS Secrets Manager for production secrets
# 3. Rotate API keys regularly
# 4. Use least privilege IAM policies
# 5. Enable CloudTrail for audit logging
