# =============================================================================
# N8N WORKFLOW AUTOMATION DOCKERFILE
# =============================================================================
# This Dockerfile creates a production-ready N8N container for AWS ECS Fargate

FROM n8nio/n8n:latest

# Set the working directory
WORKDIR /home/<USER>

# Switch to root to install additional packages if needed
USER root

# Install additional packages for enhanced functionality
RUN apk add --no-cache \
    curl \
    wget \
    git \
    python3 \
    py3-pip \
    chromium \
    chromium-chromedriver

# Install additional Node.js packages that might be useful for workflows
RUN npm install -g \
    axios \
    lodash \
    moment \
    uuid \
    crypto-js

# Create necessary directories
RUN mkdir -p /home/<USER>/.n8n \
    && chown -R node:node /home/<USER>/.n8n

# Switch back to node user for security
USER node

# Set environment variables
ENV N8N_USER_FOLDER=/home/<USER>/.n8n
ENV N8N_PORT=5678
ENV NODE_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5678/healthz || exit 1

# Expose the N8N port
EXPOSE 5678

# Start N8N
CMD ["n8n", "start"]
